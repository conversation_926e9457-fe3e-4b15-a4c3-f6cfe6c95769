@import "tailwindcss";
@import "@nuxt/ui";

@theme static {
  --font-sans: "DM Sans", "sans-serif";
}

/* Math Keyboard Grid Layouts */
.numbers-grid {
  grid-template-columns: repeat(4, 1fr);
}

.operators-grid {
  grid-template-columns: repeat(4, 1fr);
}

.inequalities-grid {
  grid-template-columns: repeat(4, 1fr);
}

.interval-grid {
  grid-template-columns: repeat(4, 1fr);
}

.set-grid {
  grid-template-columns: repeat(3, 1fr);
}

/* Mobile responsive adjustments for math keyboard */
@media (max-width: 640px) {
  .numbers-grid,
  .operators-grid,
  .inequalities-grid,
  .interval-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Ensure math expressions are readable */
.font-mono {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}
